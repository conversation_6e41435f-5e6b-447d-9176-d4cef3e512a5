import React, { useState, useEffect } from 'react';
import { Outlet, Link, useNavigate, useLocation } from 'react-router-dom';
import { 
  AppBar, 
  Box, 
  Toolbar, 
  Typography, 
  Button, 
  IconButton, 
  Drawer, 
  List, 
  ListItem, 
  ListItemIcon, 
  ListItemText, 
  Divider, 
  Container, 
  Avatar,
  Menu,
  MenuItem,
  Snackbar,
  Alert,
  CircularProgress,
  Collapse,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Badge,
  Popover
} from '@mui/material';
import SearchBar from './SearchBar';
import { useMediaQuery } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import FloatingShortcutWidget from './FloatingShortcutWidget';
import AppsModal from './AppsModal';
import MessageSidebar from './MessageSidebar';
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  Link as ShortcutIcon,
  Folder as FolderIcon,
  People as PeopleIcon,
  Vpn<PERSON>ey as RoleIcon,
  Settings as SettingsIcon,
  ExitToApp as LogoutIcon,
  Image as CanvaIcon,
  Computer as GLPIIcon,
  Event as PlanningCenterIcon,
  Storage as SynologyIcon,
  AcUnit as DreoIcon,
  Security as LenelS2NetBoxIcon,
  PhoneIphone as MosyleBusinessIcon,
  Lock as UnifiAccessIcon,
  Router as UnifiNetworkIcon,
  Videocam as UnifiProtectIcon,
  AdminPanelSettings as AdminIcon,
  ContactPage as StaffDirectoryIcon,
  CalendarMonth as GoogleCalendarIcon,
  Description as GoogleFormsIcon,
  Person as PersonIcon,
  HelpOutline as HelpIcon,
  Notifications as NotificationsIcon,
  QuestionAnswer as FAQIcon,
  Store as MarketplaceIcon,
  NetworkCheck as RadiusIcon,
  MeetingRoom as RoomBookingIcon,
  Assignment as TaskManagementIcon,
  Build as MaintenanceSchedulingIcon,
  HomeWork as BuildingManagementIcon,
  Chat as ChatIcon,
  Apple as AppleIcon,
  MusicNote as WiimIcon,
  Thermostat as SkyportCloudIcon,
  Speaker as QsysIcon,
  Lightbulb as ColoritIcon,
  Tv as ZeeVeeIcon,
  Videocam as PanasonicIcon,
  ConfirmationNumber as TicketIcon,
  Category as CategoryIcon,
  LocalOffer as TagIcon,
  Security as SecurityIcon,
  Inventory as AssetManagementIcon,
  Inventory2 as AssetsIcon,
  Assessment as ReportsIcon,
  ImportExport as ImportExportIcon,
  Dashboard as AssetDashboardIcon,
  List as AssetListIcon,
  Note as NotesIcon,
  Article as ArticleIcon,
  Newspaper as NewsIcon,
  Add as AddIcon,
  ListAlt as FormsIcon,
  ExpandLess as ExpandLessIcon,
  ExpandMore as ExpandMoreIcon,
  Search as SearchIcon,
  Apps as AppsIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import Logo from './Logo';
import * as menuItemService from '../services/menuItemService';
import notesService from '../services/notesService';
import { 
  coreNavItems, 
  quickAccessItems, 
  googleServicesItems, 
  advancedItems, 
  helpItems, 
  adminItems, 
  getNavigationItems 
} from '../config/sidebarNavigation';
import presenceService from '../services/presenceService';
import websocketService from '../services/websocketService';
import axios from 'axios';
import PermissionCheck from './PermissionCheck';
import SimplifiedSidebar from "./SimplifiedSidebar";

const ONLINE_THRESHOLD_MS = 30 * 60 * 1000; // 30 minutes

const Layout = () => {
  const { user, logout, isAdmin, hasIntegration, hasPermission } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [addMenuAnchorEl, setAddMenuAnchorEl] = useState(null);
  const [adminMenuAnchorEl, setAdminMenuAnchorEl] = useState(null);
  // For header nav items (still uses database)
  const [headerMenuItems, setHeaderMenuItems] = useState([]);
  const [menuLoading, setMenuLoading] = useState(false);
  const [menuError, setMenuError] = useState(null);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [appsModalOpen, setAppsModalOpen] = useState(false);
  const [categoryOpen, setCategoryOpen] = useState({});
  const [adminOpenSection, setAdminOpenSection] = useState(false);
  // Presence status dialog state
  const [customStatusOpen, setCustomStatusOpen] = useState(false);
  const [customStatusMessage, setCustomStatusMessage] = useState('');
  const [unreadNotifications, setUnreadNotifications] = useState(0);
  const [searchPopupOpen, setSearchPopupOpen] = useState(false);
  const [searchAnchorEl, setSearchAnchorEl] = useState(null);
  
  // Local presence state for header indicator
  const [presence, setPresence] = useState({ 
    status: 'available', 
    statusMessage: '', 
    lastActiveAt: new Date().toISOString(), 
    isActive: true,
    isOnline: false,
    lastSeenAt: new Date().toISOString()
  });
  const [presenceTick, setPresenceTick] = useState(0);
  const statusToColor = (status) => {
    switch (status) {
      case 'available': return 'success';
      case 'away': return 'warning';
      case 'lunch': return 'info';
      case 'busy': return 'error';
      case 'custom': return 'info';
      default: return 'default';
    }
  };

  // Responsive layout: persistent sidebar on desktop, temporary drawer on mobile
  const theme = useTheme();
  const isDesktop = useMediaQuery(theme.breakpoints.up('md'));
  const drawerWidth = 300;

  useEffect(() => {
    setDrawerOpen(isDesktop ? true : false);
  }, [isDesktop]);

  // Quick Add Note dialog state
  const [addNoteOpen, setAddNoteOpen] = useState(false);
  const [addNoteSubmitting, setAddNoteSubmitting] = useState(false);
  const [addNoteError, setAddNoteError] = useState(null);
  const [addNoteData, setAddNoteData] = useState({
    title: '',
    content: '',
    type: 'note',
    color: '#ffffff',
    pinned: false
  });

  // Fetch header menu items from API (optional, for admin customization)
  useEffect(() => {
    const fetchHeaderMenuItems = async () => {
      try {
        setMenuLoading(true);
        const items = await menuItemService.getMenuItems();
        // Filter only items marked for header
        const headerItems = items.filter(item => item.location === 'header' || item.showInHeader);
        setHeaderMenuItems(headerItems);
        setMenuLoading(false);
      } catch (error) {
        console.error('Error fetching header menu items:', error);
        // Silently fail - header menu items are optional
        setMenuLoading(false);
      }
    };

    if (user && isAdmin()) {
      // Only fetch header menu items for admins who might customize them
      fetchHeaderMenuItems();
    }
  }, [user, isAdmin]);

  // Initialize WebSocket and presence trackers when user is available
  useEffect(() => {
    if (!user) return;
    try {
      websocketService.connect().then(() => {
        // Authenticate with user ID after connection
        websocketService.setUserId(user.id);
      }).catch(err => console.warn('WebSocket connect failed:', err));
    } catch (e) {
      console.warn('WebSocket connect threw:', e);
    }
    presenceService.init();
    return () => {
      presenceService.destroy();
    };
  }, [user]);

  // Subscribe to notifications events via WebSocket
  useEffect(() => {
    if (!user) return;

    const handleNotification = (payload) => {
      // Increment unread count on each notification event
      setUnreadNotifications(prev => prev + 1);
    };

    // Ensure connection then subscribe
    websocketService.connect().then(() => {
      websocketService.subscribe('notification');
      websocketService.addEventListener('notification', handleNotification);
    }).catch(() => {
      // Non-fatal in dev if WS not connected
    });

    return () => {
      websocketService.removeEventListener('notification', handleNotification);
      websocketService.unsubscribe('notification');
    };
  }, [user]);

  // Fetch initial presence for header indicator
  useEffect(() => {
    if (!user) return;
    let mounted = true;
    (async () => {
      try {
        const res = await axios.get('/api/users/me');
        const u = res.data || {};
        const p = (u && u.presence) || {};
        if (mounted) {
          setPresence({
            status: p.status || 'available',
            statusMessage: p.statusMessage || '',
            lastActiveAt: p.lastActiveAt || new Date().toISOString(),
            isActive: typeof u.isActive === 'boolean' ? u.isActive : true
          });
        }
      } catch (e) {
        // Non-fatal
      }
    })();
    return () => { mounted = false; };
  }, [user]);

  // Subscribe to presence updates for current user
  useEffect(() => {
    if (!user) return;

    const handlePresence = (payload) => {
      if (!payload || !payload.userId) return;
      if (payload.userId !== user.id) return;
      const p = payload.presence || {};
      setPresence(prev => ({
        ...prev,
        status: p.status || prev.status,
        statusMessage: typeof p.statusMessage === 'string' ? p.statusMessage : prev.statusMessage,
        lastActiveAt: p.lastActiveAt || prev.lastActiveAt,
        isOnline: typeof p.isOnline === 'boolean' ? p.isOnline : prev.isOnline
      }));
    };

    const handleUserOnlineStatus = (payload) => {
      if (!payload || !payload.userId) return;
      if (payload.userId !== user.id) return;
      setPresence(prev => ({
        ...prev,
        isOnline: payload.isOnline,
        status: payload.status || prev.status,
        statusMessage: payload.statusMessage || prev.statusMessage,
        lastSeenAt: payload.lastSeenAt || prev.lastSeenAt || new Date().toISOString()
      }));
    };

    websocketService.connect().then(() => {
      websocketService.addEventListener('presence', handlePresence);
      websocketService.addEventListener('userOnlineStatus', handleUserOnlineStatus);
    }).catch(() => {});

    return () => {
      websocketService.removeEventListener('presence', handlePresence);
      websocketService.removeEventListener('userOnlineStatus', handleUserOnlineStatus);
    };
  }, [user]);

  // Ticker to refresh online badge visibility roughly each minute
  useEffect(() => {
    if (!user) return;
    const id = setInterval(() => setPresenceTick(t => t + 1), 60000);
    return () => clearInterval(id);
  }, [user]);

  const handleDrawerToggle = () => {
    setDrawerOpen(!drawerOpen);
  };

  const handleProfileMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleAddMenuOpen = (event) => {
    setAddMenuAnchorEl(event.currentTarget);
  };

  const handleAddMenuClose = () => {
    setAddMenuAnchorEl(null);
  };

  const openQuickAddNote = () => {
    setAddMenuAnchorEl(null);
    setAddNoteError(null);
    setAddNoteData({ title: '', content: '', type: 'note', color: '#ffffff', pinned: false });
    setAddNoteOpen(true);
  };

  const handleAddNoteSubmit = async () => {
    try {
      setAddNoteSubmitting(true);
      setAddNoteError(null);
      await notesService.createNote(addNoteData);
      setAddNoteSubmitting(false);
      setAddNoteOpen(false);
    } catch (err) {
      console.error('Error creating note from quick add:', err);
      setAddNoteError('Failed to create note. Please try again.');
      setAddNoteSubmitting(false);
    }
  };
  
  const handleAdminMenuOpen = (event) => {
    setAdminMenuAnchorEl(event.currentTarget);
  };

  const handleAdminMenuClose = () => {
    setAdminMenuAnchorEl(null);
  };

  const handleSearchOpen = (event) => {
    setSearchAnchorEl(event.currentTarget);
    setSearchPopupOpen(true);
  };

  const handleSearchClose = () => {
    setSearchPopupOpen(false);
    setSearchAnchorEl(null);
  };

  const handleLogout = async () => {
    await logout();
    navigate('/login');
    handleProfileMenuClose();
  };

  const handleEditProfile = () => {
    navigate(`/staff-directory/users/${user.id}`);
    handleProfileMenuClose();
  };

  // Get navigation items based on permissions (code-based)
  const navStructure = getNavigationItems(hasPermission, isAdmin, showAdvanced);

  // Integrations list (these will be moved to a separate integrations menu later)
  const integrationsList = [
    { text: 'Dashboard', icon: <DashboardIcon />, path: '/dashboard', auth: true }, // Dashboard visible to all authenticated users
    { text: 'Shortcuts', icon: <ShortcutIcon />, path: '/shortcuts', auth: true, permission: 'shortcuts:read' },
    { text: 'News', icon: <ArticleIcon />, path: '/news', auth: true, permission: 'news:read' },
    { text: 'Help Tickets', icon: <TicketIcon />, path: '/tickets', auth: true, permission: 'tickets:read' },
    { text: 'Forms', icon: <FormsIcon />, path: '/forms', auth: true, permission: 'forms:read' },
    { text: 'Drive Files', icon: <FolderIcon />, path: '/drive', auth: true, permission: 'googleDrive:read' },
    { text: 'Google Calendar', icon: <GoogleCalendarIcon />, path: '/google-calendar', auth: true, permission: 'googleCalendar:read' },
    { text: 'Google Forms', icon: <GoogleFormsIcon />, path: '/google-forms', auth: true, permission: 'googleForms:read' },
    { text: 'Staff Directory', icon: <StaffDirectoryIcon />, path: '/staff-directory', auth: true, permission: 'staffDirectory:read' },
    { text: 'People', icon: <PeopleIcon />, path: '/people', auth: true, permission: 'people:read' },
    { text: 'Tasks', icon: <TaskManagementIcon />, path: '/tasks', auth: true, permission: 'tasks:read' },
    { text: 'Asset Management (Legacy)', icon: <AssetManagementIcon />, path: '/asset-management', auth: true, permission: 'assets:read' },
    { text: 'Assets', icon: <AssetsIcon />, path: '/assets', auth: true, permission: 'assets:read' },
    { text: 'Asset List', icon: <AssetListIcon />, path: '/assets/list', auth: true, permission: 'assets:read' },
    { text: 'Asset Reports', icon: <ReportsIcon />, path: '/assets/reports', auth: true, permission: 'assets:read' },
    { text: 'Asset Import/Export', icon: <ImportExportIcon />, path: '/assets/import-export', auth: true, permission: 'assets:admin' },
    { text: 'Notes', icon: <NotesIcon />, path: '/notes', auth: true, permission: 'notes:read' },
    { text: 'Room Booking', icon: <RoomBookingIcon />, path: '/room-booking', auth: true, permission: 'roomBooking:read' },
    { text: 'Building Management', icon: <BuildingManagementIcon />, path: '/building-management', auth: true, permission: 'buildingManagement:read' },
    { text: 'Help Center', icon: <HelpIcon />, path: '/help', auth: true, permission: 'help:read' },
    { text: 'FAQs', icon: <FAQIcon />, path: '/help/faq', auth: true, permission: 'help:read' }
  ];

  const integrationItems = [
    { id: 'canva', text: 'Canva', icon: <CanvaIcon />, path: '/canva', auth: true, permission: 'canva:read' },
    { id: 'planning-center', text: 'Planning Center', icon: <PlanningCenterIcon />, path: '/planning-center', auth: true, permission: 'planningCenter:read' },
    { id: 'synology', text: 'Synology', icon: <SynologyIcon />, path: '/synology', auth: true, permission: 'synology:read' },
    { id: 'dreo', text: 'Dreo', icon: <DreoIcon />, path: '/dreo', auth: true, permission: 'dreo:read' },
    { id: 'lgThinq', text: 'LG ThinQ AC', icon: <DreoIcon />, path: '/lg-thinq', auth: true, permission: 'lgThinq:read' },
    { id: 'lenel-s2-netbox', text: 'Lenel S2 NetBox', icon: <LenelS2NetBoxIcon />, path: '/lenel-s2-netbox', auth: true, permission: 'lenelS2NetBox:read' },
    { id: 'mosyle-business', text: 'Mosyle Business', icon: <MosyleBusinessIcon />, path: '/mosyle-business', auth: true, permission: 'mosyleBusiness:read' },
    { id: 'unifi-access', text: 'UniFi Access', icon: <UnifiAccessIcon />, path: '/unifi-access', auth: true, permission: 'unifiAccess:read' },
    { id: 'unifi-network', text: 'UniFi Network', icon: <UnifiNetworkIcon />, path: '/unifi-network', auth: true, permission: 'unifiNetwork:read' },
    { id: 'unifi-protect', text: 'UniFi Protect', icon: <UnifiProtectIcon />, path: '/unifi-protect', auth: true, permission: 'unifiProtect:read' },
    { id: 'google-admin', text: 'Google Admin', icon: <AdminIcon />, path: '/google-admin', auth: true, permission: 'googleAdmin:read' },
    { id: 'google-forms', text: 'Google Forms', icon: <GoogleFormsIcon />, path: '/google-forms', auth: true, permission: 'googleForms:read' },
    { id: 'radius', text: 'RADIUS', icon: <RadiusIcon />, path: '/radius', auth: true, permission: 'radius:read' },
    { id: 'apple-business-manager', text: 'Apple Business Manager', icon: <AppleIcon />, path: '/apple-business-manager', auth: true, permission: 'appleBusinessManager:read' },
    { id: 'rain-bird', text: 'Rain Bird', icon: <SkyportCloudIcon />, path: '/rain-bird', auth: true, permission: 'rainBird:read' },
    { id: 'wiim', text: 'WiiM', icon: <WiimIcon />, path: '/wiim', auth: true, permission: 'wiim:read' },
    { id: 'skyportcloud', text: 'SkyportCloud HVAC', icon: <SkyportCloudIcon />, path: '/skyportcloud', auth: true, permission: 'skyportcloud:read' },
    { id: 'qsys', text: 'Q-sys Core Manager', icon: <QsysIcon />, path: '/qsys', auth: true, permission: 'qsys:read' },
    { id: 'colorlit', text: 'Colorlit LED Controller', icon: <ColoritIcon />, path: '/colorlit', auth: true, permission: 'colorlit:read' },
    { id: 'zeevee', text: 'ZeeVee HDbridge', icon: <ZeeVeeIcon />, path: '/zeevee', auth: true, permission: 'zeevee:read' },
    { id: 'panasonic', text: 'Panasonic Pro AV Camera', icon: <PanasonicIcon />, path: '/panasonic', auth: true, permission: 'panasonic:read' }
  ];

  // Legacy admin navigation items for compatibility
  const legacyAdminNavItems = [
    { text: 'System Status', icon: <AdminIcon />, path: '/admin/status', admin: true },
    { text: 'Manage Users', icon: <PeopleIcon />, path: '/admin/users', admin: true, permission: 'users:admin' },
    { text: 'Manage Roles', icon: <RoleIcon />, path: '/admin/roles', admin: true, permission: 'roles:admin' },
    { text: 'Manage Shortcuts', icon: <SettingsIcon />, path: '/admin/shortcuts', admin: true, permission: 'shortcuts:admin' },
    { text: 'Manage Menu', icon: <MenuIcon />, path: '/admin/menu', admin: true, permission: 'menu:admin' },
    { text: 'Manage RADIUS', icon: <RadiusIcon />, path: '/admin/radius', admin: true, permission: 'radius:admin' },
    { text: 'Manage Building Management', icon: <BuildingManagementIcon />, path: '/admin/building-management', admin: true, permission: 'buildingManagement:admin' },
    { text: 'Manage Ticket Categories & Tags', icon: <CategoryIcon />, path: '/admin/ticket-categories-and-tags', admin: true, permission: 'tickets:admin' },
    { text: 'Access Control', icon: <SecurityIcon />, path: '/access-control', admin: true, permission: 'accessControl:admin' }
  ];

  // Helper function to render icon based on icon name or use custom icon
  const renderIcon = (item) => {
    if (item.customIcon) {
      return <Avatar src={item.customIcon} sx={{ width: 24, height: 24 }} />;
    }
    
    // If using database items, the icon is a string name
    if (typeof item.icon === 'string') {
      // Map icon names to Material-UI icons
      const iconMap = {
        'dashboard': <DashboardIcon />,
        'link': <ShortcutIcon />,
        'folder': <FolderIcon />,
        'people': <PeopleIcon />,
        'vpn_key': <RoleIcon />,
        'settings': <SettingsIcon />,
        'image': <CanvaIcon />,
        'computer': <GLPIIcon />,
        'event': <PlanningCenterIcon />,
        'storage': <SynologyIcon />,
        'ac_unit': <DreoIcon />,
        'security': <SecurityIcon />,
        'phone_iphone': <MosyleBusinessIcon />,
        'lock': <UnifiAccessIcon />,
        'router': <UnifiNetworkIcon />,
        'videocam': <UnifiProtectIcon />,
        'admin_panel_settings': <AdminIcon />,
        'contact_page': <StaffDirectoryIcon />,
        'calendar_month': <GoogleCalendarIcon />,
        'description': <GoogleFormsIcon />,
        'person': <PersonIcon />,
        'help_outline': <HelpIcon />,
        'question_answer': <FAQIcon />,
        'network_check': <RadiusIcon />,
        'meeting_room': <RoomBookingIcon />,
        'assignment': <TaskManagementIcon />,
        'build': <MaintenanceSchedulingIcon />,
        'home_work': <BuildingManagementIcon />,
        'apple': <AppleIcon />,
        'music_note': <WiimIcon />,
        'thermostat': <SkyportCloudIcon />,
        'speaker': <QsysIcon />,
        'lightbulb': <ColoritIcon />,
        'tv': <ZeeVeeIcon />,
        'confirmation_number': <TicketIcon />,
        'category': <CategoryIcon />,
        'local_offer': <TagIcon />,
        'menu': <MenuIcon />,
        'apps': <AppsIcon />,
        'inventory2': <AssetsIcon />,
        'assessment': <ReportsIcon />,
        'import_export': <ImportExportIcon />,
        'list': <AssetListIcon />,
        'article': <ArticleIcon />,
        'newspaper': <NewsIcon />,
        'note': <NotesIcon />
      };
      
      return iconMap[item.icon] || <ShortcutIcon />;
    }
    
    // If using static items, the icon is already a React element
    return item.icon;
  };

  // Use code-based navigation structure
  const displayNavItems = [...navStructure.core, ...navStructure.quickAccess];
  const displayGoogleItems = navStructure.googleServices;
  const displayAdvancedItems = navStructure.advanced;
  const displayHelpItems = navStructure.help;
  const displayAdminNavItems = navStructure.admin;
  const displayIntegrationItems = hasPermission('integrations:view') ? integrationItems.filter(item => {
    const perm = item.permission;
    if (!perm) return true;
    if (isAdmin()) return true;
    return hasPermission(perm);
  }) : [];

  // Helpers for submenu grouping
  const getItemText = (item) => item.friendlyName || item.title || item.text;
  const getItemPermission = (item) => item.requiredPermission || item.permission;
  const getItemCategories = (item) => item.categories || [];
  const isAssetItem = (item) => {
    const path = (item.path || '').toLowerCase();
    return path === '/assets' || path.startsWith('/assets/');
  };
  const isGoogleItem = (item) => {
    const cats = getItemCategories(item);
    const text = (getItemText(item) || '').toLowerCase();
    const path = (item.path || '').toLowerCase();
    return (
      cats.includes('Google Services') ||
      text.includes('google') ||
      path.startsWith('/google') ||
      path === '/drive'
    );
  };

  // Build Google submenu items from both nav and integrations, filter by permission and dedupe by path
  const googleMap = new Map();
  [...displayNavItems, ...displayIntegrationItems]
    .filter((it) => isGoogleItem(it))
    .forEach((it) => {
      const perm = getItemPermission(it);
      if (!perm || hasPermission(perm)) {
        googleMap.set(it.path, it);
      }
    });
  const googleItems = Array.from(googleMap.values()).sort((a, b) => getItemText(a).localeCompare(getItemText(b)));

  // Build Assets submenu items from nav items, filter by permission and dedupe by path
  const assetMap = new Map();
  displayNavItems
    .filter((it) => isAssetItem(it))
    .forEach((it) => {
      const perm = getItemPermission(it);
      if (!perm || hasPermission(perm)) {
        assetMap.set(it.path, it);
      }
    });
  const assetItems = Array.from(assetMap.values()).sort((a, b) => {
    const ao = (a.order ?? 0);
    const bo = (b.order ?? 0);
    if (ao !== bo) return ao - bo;
    return getItemText(a).localeCompare(getItemText(b));
  });

  // Exclude Google items from the main lists to avoid duplication
  const displayNavItemsFiltered = displayNavItems.filter((it) => !isGoogleItem(it) && !isAssetItem(it));
  const displayIntegrationItemsFiltered = displayIntegrationItems.filter((it) => !isGoogleItem(it));

  // Consistent sorting helper
  const sortByOrderThenText = (a, b) => {
    const ao = (a.order ?? 0);
    const bo = (b.order ?? 0);
    if (ao !== bo) return ao - bo;
    return getItemText(a).localeCompare(getItemText(b));
  };

  // Sorted lists to reduce confusion
  const mainItemsSorted = [...displayNavItems].sort(sortByOrderThenText);
  const integrationItemsSorted = [...displayIntegrationItems].sort(sortByOrderThenText);

  // Admin items for sidebar section (filtered by permission)
  const adminItems = isAdmin() ? displayAdminNavItems.filter((item) => {
    const perm = getItemPermission(item);
    return (!perm || hasPermission(perm)) && !item.isDivider && !item.isHeader;
  }) : [];
  const adminItemsSorted = [...adminItems].sort(sortByOrderThenText);

  // Group regular navigation items by non-core categories defined in managed menu system
  const navCategoryMap = {};
  const groupedPathsSet = new Set();
  displayNavItems.forEach((it) => {
    const cats = getItemCategories(it);
    const nonCoreCats = cats.filter((c) => c && c !== 'Core Features');
    if (nonCoreCats.length > 0) {
      nonCoreCats.forEach((cat) => {
        if (!navCategoryMap[cat]) navCategoryMap[cat] = [];
        navCategoryMap[cat].push(it);
        if (it.path) groupedPathsSet.add(it.path);
      });
    }
  });
  const categoryNamesSorted = Object.keys(navCategoryMap).sort();
  const topLevelMainItems = mainItemsSorted.filter((it) => {
    const cats = getItemCategories(it);
    const nonCoreCats = cats.filter((c) => c && c !== 'Core Features');
    return nonCoreCats.length === 0;
  });

  // Auto-open category groups and admin section based on active route
  useEffect(() => {
    const currentPath = (location.pathname || '').toLowerCase();
    const newOpen = { ...categoryOpen };
    // Open any category that contains the current path
    categoryNamesSorted.forEach((cat) => {
      const items = navCategoryMap[cat] || [];
      newOpen[cat] = items.some((it) => (it.path || '').toLowerCase() === currentPath || (it.path || '').toLowerCase().startsWith(currentPath.split('/').slice(0,3).join('/')));
    });
    setCategoryOpen(newOpen);

    const shouldOpenAdmin = currentPath.startsWith('/admin') || currentPath.startsWith('/access-control');
    setAdminOpenSection(shouldOpenAdmin);
  }, [location.pathname, categoryNamesSorted.length]);

  // Determine required permission for the current route based on managed menu items
  const currentPathLower = (location.pathname || '').toLowerCase();
  const allGuardItems = [
    ...displayNavItems,
    ...displayIntegrationItems,
    ...adminItemsSorted,
    ...googleItems,
    ...assetItems
  ];
  let routeRequiredPermission = null;
  let bestMatchLength = -1;
  for (const it of allGuardItems) {
    const p = (it.path || '').toLowerCase();
    if (!p || it.isDivider || it.isHeader) continue;
    const isExact = currentPathLower === p;
    const isPrefix = currentPathLower.startsWith(p.endsWith('/') ? p : (p + '/'));
    if (isExact || isPrefix) {
      const perm = getItemPermission(it);
      if (perm && p.length > bestMatchLength) {
        bestMatchLength = p.length;
        routeRequiredPermission = perm;
      }
    }
  }

  const drawer = (
    <Box sx={{ width: drawerWidth, height: '100%', display: 'flex', flexDirection: 'column' }} role="navigation" aria-label="Primary" onClick={!isDesktop ? handleDrawerToggle : undefined}>
      {/* Logo Section - Compact */}
      <Box sx={{ 
        padding: '1rem 1rem',
        borderBottom: '1px solid rgba(0, 0, 0, 0.1)',
        textAlign: 'center',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        flexShrink: 0
      }}>
        <Logo height={48} variant="full" showText={false} sx={{ mb: 1 }} />
        <Typography variant="h6" component="h1" sx={{ 
          color: '#374151', 
          fontSize: '0.95rem', 
          fontWeight: 700, 
          lineHeight: 1.2, 
          mb: 0.25
        }}>
          Christian Student Fellowship
        </Typography>
        <Typography variant="body2" sx={{ 
          color: '#6b7280', 
          fontSize: '0.75rem',
          fontWeight: 500
        }}>
          Staff Portal
        </Typography>
      </Box>
      
      {menuLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress size={24} />
        </Box>
      ) : (
        <>
          {/* Simplified Navigation Menu */}
          <SimplifiedSidebar 
            navStructure={navStructure}
            categoryOpen={categoryOpen}
            setCategoryOpen={setCategoryOpen}
            showAdvanced={showAdvanced}
            setShowAdvanced={setShowAdvanced}
            user={user}
            isAdmin={isAdmin}
            adminOpenSection={adminOpenSection}
            setAdminOpenSection={setAdminOpenSection}
          />
        </>
      )}

  return (
    <Box sx={{ 
      display: 'flex', 
      flexDirection: 'column', 
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #3b82f6 100%)',
      backgroundAttachment: 'fixed'
    }}>
      {user && (
      <AppBar position="static" sx={{ ...(user && isDesktop ? { width: `calc(100% - ${drawerWidth}px)`, ml: `${drawerWidth}px` } : {}) }}>
        <Toolbar>
          {user && !isDesktop && (
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2 }}
            >
              <MenuIcon />
            </IconButton>
          )}
          
          {/* FloatingShortcutWidget integrated into header - left side */}
          {user && (
            <Box sx={{ flexGrow: 1, display: 'flex', alignItems: 'center' }}>
              <FloatingShortcutWidget inHeader={true} />
            </Box>
          )}
          
          {user ? (
            <>
              {/* Search popup icon */}
              <IconButton
                color="inherit"
                size="small"
                onClick={handleSearchOpen}
                aria-label="search"
                sx={{ ml: 1 }}
              >
                <SearchIcon />
              </IconButton>
              {isAdmin() && (
                <IconButton
                  color="inherit"
                  size="small"
                  sx={{ ml: 2 }}
                  aria-label="admin"
                  aria-controls="admin-menu"
                  aria-haspopup="true"
                  onClick={handleAdminMenuOpen}
                >
                  <AdminIcon />
                </IconButton>
              )}
              <IconButton
                color="inherit"
                size="small"
                sx={{ ml: 2 }}
                aria-label="add"
                aria-controls="add-menu"
                aria-haspopup="true"
                onClick={handleAddMenuOpen}
              >
                <AddIcon />
              </IconButton>
              <IconButton
                color="inherit"
                size="small"
                sx={{ ml: 2 }}
                aria-label="notifications"
                onClick={() => setUnreadNotifications(0)}
              >
                <Badge color="error" badgeContent={unreadNotifications} invisible={unreadNotifications === 0}>
                  <NotificationsIcon />
                </Badge>
              </IconButton>
              <IconButton
                color="inherit"
                size="small"
                sx={{ ml: 2 }}
                aria-label="apps"
                onClick={() => setAppsModalOpen(true)}
              >
                <AppsIcon />
              </IconButton>
              <IconButton
                component={Link}
                to="/help"
                color="inherit"
                size="small"
                sx={{ ml: 2 }}
                aria-label="help"
              >
                <HelpIcon />
              </IconButton>
              <Menu
                id="profile-menu"
                anchorEl={anchorEl}
                keepMounted
                open={Boolean(anchorEl)}
                onClose={handleProfileMenuClose}
              >
                <MenuItem disabled>
                  <Typography variant="body2">{user.email}</Typography>
                </MenuItem>
                <Divider />
                <MenuItem onClick={handleEditProfile}>
                  <ListItemIcon>
                    <PersonIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary="Edit Profile" />
                </MenuItem>
                <Divider />
                <MenuItem selected={presence.status === 'available'} onClick={async () => { setPresence(p => ({ ...p, status: 'available', statusMessage: '' })); await presenceService.setStatus('available'); handleProfileMenuClose(); }}>
                  <ListItemText primary="Set Available" />
                </MenuItem>
                <MenuItem selected={presence.status === 'away'} onClick={async () => { setPresence(p => ({ ...p, status: 'away' })); await presenceService.setStatus('away'); handleProfileMenuClose(); }}>
                  <ListItemText primary="Set Away" />
                </MenuItem>
                <MenuItem selected={presence.status === 'lunch'} onClick={async () => { setPresence(p => ({ ...p, status: 'lunch', statusMessage: 'At lunch' })); await presenceService.setStatus('lunch', 'At lunch'); handleProfileMenuClose(); }}>
                  <ListItemText primary="At Lunch" />
                </MenuItem>
                <MenuItem selected={presence.status === 'busy'} onClick={async () => { setPresence(p => ({ ...p, status: 'busy', statusMessage: 'Busy' })); await presenceService.setStatus('busy', 'Busy'); handleProfileMenuClose(); }}>
                  <ListItemText primary="Set Busy" />
                </MenuItem>
                <MenuItem selected={presence.status === 'custom'} onClick={() => { setCustomStatusOpen(true); handleProfileMenuClose(); }}>
                  <ListItemText primary="Custom Status…" />
                </MenuItem>
                <Divider />
                <MenuItem onClick={handleLogout}>
                  <ListItemIcon>
                    <LogoutIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary="Logout" />
                </MenuItem>
              </Menu>

              {/* Custom Status Dialog */}
              <Dialog open={customStatusOpen} onClose={() => setCustomStatusOpen(false)}>
                <DialogTitle>Set Custom Status</DialogTitle>
                <DialogContent>
                  <TextField
                    autoFocus
                    margin="dense"
                    id="custom-status-message"
                    label="Status message"
                    type="text"
                    fullWidth
                    variant="standard"
                    value={customStatusMessage}
                    onChange={(e) => setCustomStatusMessage(e.target.value)}
                  />
                </DialogContent>
                <DialogActions>
                  <Button onClick={() => setCustomStatusOpen(false)}>Cancel</Button>
                  <Button onClick={async () => { setPresence(p => ({ ...p, status: 'custom', statusMessage: customStatusMessage })); await presenceService.setStatus('custom', customStatusMessage); setCustomStatusOpen(false); setCustomStatusMessage(''); handleProfileMenuClose(); }}>Save</Button>
                </DialogActions>
              </Dialog>
              
              <Menu
                id="admin-menu"
                anchorEl={adminMenuAnchorEl}
                keepMounted
                open={Boolean(adminMenuAnchorEl)}
                onClose={handleAdminMenuClose}
              >
                {displayAdminNavItems
                  .filter(item => {
                    const itemPermission = item.requiredPermission || item.permission;
                    return !itemPermission || hasPermission(itemPermission);
                  })
                  .map((item) => {
                    const itemText = item.friendlyName || item.title || item.text;
                    const itemPath = item.path;
                    
                    if (item.isDivider) {
                      return <Divider key={`divider-${itemText}`} />;
                    }

                    if (item.isHeader) {
                      return (
                        <Box key={`header-${itemText}`} sx={{ p: 2 }}>
                          <Typography variant="subtitle2" color="text.secondary">
                            {itemText}
                          </Typography>
                        </Box>
                      );
                    }

                    return (
                      <MenuItem 
                        key={itemText} 
                        component={Link} 
                        to={itemPath}
                        onClick={handleAdminMenuClose}
                      >
                        <ListItemIcon>{renderIcon(item)}</ListItemIcon>
                        <ListItemText primary={itemText} />
                      </MenuItem>
                    );
                  })}
              </Menu>
              
              <Menu
                id="add-menu"
                anchorEl={addMenuAnchorEl}
                keepMounted
                open={Boolean(addMenuAnchorEl)}
                onClose={handleAddMenuClose}
              >
                {hasPermission('tickets:create') && (
                  <MenuItem 
                    component={Link} 
                    to="/tickets/new" 
                    onClick={handleAddMenuClose}
                  >
                    <ListItemIcon>
                      <TicketIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary="Create Support Ticket" />
                  </MenuItem>
                )}
                
                {hasPermission('news:create') && (
                  <MenuItem 
                    component={Link} 
                    to="/news/posts/new" 
                    onClick={handleAddMenuClose}
                  >
                    <ListItemIcon>
                      <NewsIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary="Add News Item" />
                  </MenuItem>
                )}
                
                {hasPermission('forms:create') && (
                  <MenuItem 
                    component={Link} 
                    to="/forms/new" 
                    onClick={handleAddMenuClose}
                  >
                    <ListItemIcon>
                      <FormsIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary="Create Form" />
                  </MenuItem>
                )}
                
                {hasPermission('notes:create') && (
                  <MenuItem onClick={openQuickAddNote}>
                    <ListItemIcon>
                      <NotesIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary="Add Note" />
                  </MenuItem>
                )}
                
                {hasPermission('tasks:create') && (
                  <MenuItem 
                    component={Link} 
                    to="/tasks/new" 
                    onClick={handleAddMenuClose}
                  >
                    <ListItemIcon>
                      <TaskManagementIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary="Add Task" />
                  </MenuItem>
                )}
                
                {hasPermission('assets:create') && (
                  <MenuItem 
                    component={Link} 
                    to="/assets/new" 
                    onClick={handleAddMenuClose}
                  >
                    <ListItemIcon>
                      <AssetsIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary="Add Asset" />
                  </MenuItem>
                )}
              </Menu>
            </>
          ) : null}
        </Toolbar>
      </AppBar>
      )}

      <Box sx={{ display: 'flex', flexGrow: 1, width: '100%', maxWidth: '100%', overflowX: 'hidden' }}>
        {user && (
          <Drawer
            anchor="left"
            variant={isDesktop ? 'permanent' : 'temporary'}
            open={isDesktop ? true : drawerOpen}
            onClose={!isDesktop ? handleDrawerToggle : undefined}
            ModalProps={{ keepMounted: true }}
            sx={{ 
              width: isDesktop ? drawerWidth : 0, 
              flexShrink: 0, 
              '& .MuiDrawer-paper': { 
                width: drawerWidth, 
                boxSizing: 'border-box',
                background: 'rgba(255, 255, 255, 0.98)',
                backdropFilter: 'blur(20px)',
                WebkitBackdropFilter: 'blur(20px)',
                boxShadow: '4px 0 30px rgba(0, 0, 0, 0.1), inset -1px 0 0 rgba(255, 255, 255, 0.2)',
                border: 'none',
                borderRight: '1px solid rgba(255, 255, 255, 0.2)'
              } 
            }}
          >
            {drawer}
          </Drawer>
        )}

        <Container component="main" maxWidth={isDesktop ? false : 'lg'} sx={{ 
          flexGrow: 1, 
          py: 3, 
          width: '100%', 
          maxWidth: '100%', 
          overflowX: 'hidden',
          background: 'transparent'
        }}>
          {user && routeRequiredPermission ? (
            <PermissionCheck requiredPermission={routeRequiredPermission}>
              <Outlet />
            </PermissionCheck>
          ) : (
            <Outlet />
          )}
        </Container>
      </Box>

      <Box
        component="footer"
        sx={{
          py: 3,
          px: 2,
          mt: 'auto',
          backgroundColor: (theme) => theme.palette.grey[100],
          ...(user && isDesktop ? { width: `calc(100% - ${drawerWidth}px)`, ml: `${drawerWidth}px` } : {})
        }}
      >
        <Container maxWidth="sm">
          <Typography variant="body2" color="text.secondary" align="center">
            © {new Date().getFullYear()} CSF Staff Portal. All rights reserved.
          </Typography>
        </Container>
      </Box>
      
      {/* Search Popup */}
      {user && (
        <Popover
          open={searchPopupOpen}
          anchorEl={searchAnchorEl}
          onClose={handleSearchClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
          sx={{ mt: 1 }}
        >
          <Box sx={{ p: 2, minWidth: 400 }}>
            <SearchBar onClose={handleSearchClose} />
          </Box>
        </Popover>
      )}
      
      {/* Message Sidebar - only shown for authenticated users with permission */}
      {user && hasPermission('messages:read') && <MessageSidebar />}
      
      {/* Apps Modal */}
      {user && <AppsModal open={appsModalOpen} onClose={() => setAppsModalOpen(false)} />}

      {/* Quick Add Note Dialog */}
      {user && (
        <Dialog open={addNoteOpen} onClose={() => setAddNoteOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>New Note</DialogTitle>
          <DialogContent>
            {addNoteError && (
              <Box sx={{ mt: 1 }}>
                <Alert severity="error">{addNoteError}</Alert>
              </Box>
            )}
            <Box sx={{ mt: 1 }}>
              <TextField
                margin="normal"
                required
                fullWidth
                id="quick-note-title"
                label="Title"
                name="title"
                value={addNoteData.title}
                onChange={(e) => setAddNoteData({ ...addNoteData, title: e.target.value })}
              />
              <TextField
                margin="normal"
                fullWidth
                id="quick-note-content"
                label="Content"
                name="content"
                multiline
                rows={4}
                value={addNoteData.content}
                onChange={(e) => setAddNoteData({ ...addNoteData, content: e.target.value })}
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setAddNoteOpen(false)} disabled={addNoteSubmitting}>Cancel</Button>
            <Button onClick={handleAddNoteSubmit} variant="contained" disabled={!addNoteData.title || addNoteSubmitting}>
              {addNoteSubmitting ? 'Saving...' : 'Save'}
            </Button>
          </DialogActions>
        </Dialog>
      )}
    </Box>
  )
            };

export default Layout;
