# Integration Status Tracker

Last Updated: 2025-08-26T21:08:35.667Z

| Integration | Status | Last Checked | Last Updated | Notes |
| ----------- | ------ | ------------ | ------------ | ----- |
| ----------- | ------ | 2025-08-21T02:42:40.119Z | N/A | ----- |
| UniFi Protect | error | 2025-08-26T21:08:05.661Z | N/A | Error: Failed to connect to all UniFi Protect instances: Instance A: instance.api.bootstrap is not a function |
| Google Forms | active | 2025-08-26T21:08:04.819Z | 2025-08-26T21:08:04.819Z | Integration is properly authenticated using service account. |
| SkyportCloud | error | 2025-08-26T21:08:05.076Z | N/A | Error: Request failed with status code 404 |
| UniFi Access | not_configured | 2025-08-26T21:08:08.830Z | N/A | Authentication failed. Check credentials and try again. |
| UniFi Network | error | 2025-08-26T21:08:09.412Z | N/A | Error: Failed to connect to UniFi Network Controller at **********:8443. Error: Authentication failed: Error: API Error: 404 - "" |
| Lenel S2 NetBox | error | 2025-08-26T21:08:35.667Z | N/A | Authentication error: getaddrinfo ENOTFOUND host-172-16-0-71 |
| RADIUS | active | 2025-08-26T21:08:07.445Z | 2025-08-26T21:08:07.445Z | RADIUS server is running with static authentication. |
| WiiM | error | 2025-08-26T21:04:06.313Z | N/A | Error: connect ENETUNREACH ************:443 |
| Google Admin | active | 2025-08-26T21:07:56.028Z | 2025-08-26T21:07:56.028Z | Integration is properly authenticated using service account. |
